#!/usr/bin/env python3
"""
BTC价格预测反思学习脚本
功能：
1. 验证之前的预测结果准确性
2. 从错误中学习，进行增量训练
3. 更新模型以提高未来预测准确性
4. 清理预测日志，为下一轮预测做准备

使用方法：
python daily_reflection.py

或设置crontab定时任务（建议每天凌晨执行）：
0 2 * * * cd /ai-model && python daily_reflection.py >> logs/reflection.log 2>&1
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import joblib
import os
import requests
import warnings
import ta
from datetime import datetime, timedelta

warnings.filterwarnings('ignore', category=UserWarning)

# --- 配置参数 ---
MODEL_DIR = "btc_reflection_model_v1"
PREDICTION_LOG_PATH = os.path.join(MODEL_DIR, "prediction_log.csv")
REFLECTION_DATA_PATH = os.path.join(MODEL_DIR, "reflection_data.csv")
BINANCE_API_URL = "https://api.binance.com/api/v3/klines"
PREDICTION_WINDOW = 1  # 预测未来1个周期（30分钟）

# 反思学习配置
MIN_PREDICTION_SAMPLES = 24  # 最少需要24条预测记录（12小时）
MIN_ERROR_SAMPLES = 5       # 最少错误样本数量
ACCURACY_THRESHOLD = 0.55   # 准确率阈值，低于此值触发重训练
DAYS_BACK = 7              # 获取历史数据的天数

def create_features(df):
    """
    从原始K线数据创建特征（与训练和预测脚本完全一致）
    """
    df_feat = df.copy()

    try:
        # 使用 ta 库计算技术指标，确保与训练和预测脚本完全一致
        # 动量指标
        df_feat['momentum_rsi'] = ta.momentum.RSIIndicator(df_feat['close']).rsi()

        # 趋势指标
        macd = ta.trend.MACD(df_feat['close'])
        df_feat['trend_macd'] = macd.macd()
        df_feat['trend_macd_signal'] = macd.macd_signal()
        df_feat['trend_macd_diff'] = macd.macd_diff()

        # 波动率指标 (布林带)
        bb = ta.volatility.BollingerBands(df_feat['close'])
        df_feat['volatility_bbm'] = bb.bollinger_mavg()
        df_feat['volatility_bbh'] = bb.bollinger_hband()
        df_feat['volatility_bbl'] = bb.bollinger_lband()
        df_feat['volatility_bbw'] = bb.bollinger_wband()
        df_feat['volatility_bbp'] = bb.bollinger_pband()

        # ADX 趋势强度指标
        adx = ta.trend.ADXIndicator(df_feat['high'], df_feat['low'], df_feat['close'])
        df_feat['trend_adx'] = adx.adx()
        df_feat['trend_adx_pos'] = adx.adx_pos()
        df_feat['trend_adx_neg'] = adx.adx_neg()

        # 成交量指标
        df_feat['volume_obv'] = ta.volume.OnBalanceVolumeIndicator(df_feat['close'], df_feat['volume']).on_balance_volume()

        # 基础价格特征
        df_feat['price_change'] = df_feat['close'].pct_change()
        df_feat['high_low_ratio'] = df_feat['high'] / df_feat['low']
        df_feat['body_ratio'] = abs(df_feat['close'] - df_feat['open']) / (df_feat['high'] - df_feat['low'] + 1e-9)

        # 多时间框架特征
        windows = [3, 5, 10, 20, 50, 100]
        for window in windows:
            df_feat[f'ma_{window}'] = df_feat['close'].rolling(window=window).mean()
            df_feat[f'price_ma_{window}_ratio'] = df_feat['close'] / (df_feat[f'ma_{window}'] + 1e-9)
            df_feat[f'momentum_{window}'] = df_feat['close'] / (df_feat['close'].shift(window) + 1e-9) - 1
            df_feat[f'volatility_{window}'] = df_feat['close'].rolling(window=window).std()

        # 额外的技术指标（保持与训练和预测脚本一致）
        delta = df_feat['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-9)
        df_feat['rsi_14'] = 100 - (100 / (1 + rs))

        ema_fast = df_feat['close'].ewm(span=12).mean()
        ema_slow = df_feat['close'].ewm(span=26).mean()
        df_feat['macd'] = ema_fast - ema_slow
        df_feat['macd_signal'] = df_feat['macd'].ewm(span=9).mean()
        df_feat['macd_hist'] = df_feat['macd'] - df_feat['macd_signal']

        df_feat['volume_ma_20'] = df_feat['volume'].rolling(window=20).mean()
        df_feat['volume_ratio_20'] = df_feat['volume'] / (df_feat['volume_ma_20'] + 1e-9)
        df_feat['volume_spike'] = (df_feat['volume'] > df_feat['volume_ma_20'] * 2).astype(int)

        # 清理数据
        df_feat = df_feat.replace([np.inf, -np.inf], np.nan)
        df_feat.dropna(inplace=True)

        return df_feat

    except Exception as e:
        print(f"特征工程过程出错: {e}")
        return pd.DataFrame()

def fetch_historical_klines(symbol='BTCUSDT', interval='30m', days_back=DAYS_BACK):
    """
    获取历史K线数据用于验证预测结果（支持大量数据分批获取）

    Args:
        symbol: 交易对
        interval: K线间隔
        days_back: 获取过去几天的数据

    Returns:
        pandas.DataFrame: 历史K线数据
    """
    try:
        # 计算需要获取的K线数量
        # 30分钟间隔，每天48根K线
        total_limit = days_back * 48

        print(f"正在获取过去{days_back}天的历史K线数据（需要{total_limit}条）...")

        all_data = []

        # 如果需要的数据量超过1000条，分批获取
        if total_limit <= 1000:
            # 单次获取
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': total_limit
            }

            response = requests.get(BINANCE_API_URL, params=params, timeout=10)
            response.raise_for_status()
            all_data = response.json()

        else:
            # 分批获取
            print(f"数据量较大，将分批获取...")
            remaining = total_limit
            end_time = None

            while remaining > 0:
                batch_limit = min(1000, remaining)

                params = {
                    'symbol': symbol,
                    'interval': interval,
                    'limit': batch_limit
                }

                # 如果不是第一批，设置结束时间
                if end_time is not None:
                    params['endTime'] = end_time

                response = requests.get(BINANCE_API_URL, params=params, timeout=10)
                response.raise_for_status()
                batch_data = response.json()

                if not batch_data:
                    break

                # 将新数据添加到开头（因为我们是从最新往回获取）
                all_data = batch_data + all_data

                # 更新结束时间为当前批次最早的时间
                end_time = batch_data[0][0] - 1  # 减1毫秒避免重复
                remaining -= len(batch_data)

                print(f"已获取 {len(all_data)} 条数据，剩余 {remaining} 条...")

                # 避免请求过于频繁
                import time
                time.sleep(0.1)

        if not all_data:
            print("未获取到任何数据")
            return None

        # 转换为DataFrame
        df = pd.DataFrame(all_data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])

        # 数据类型转换
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume', 'number_of_trades']:
            df[col] = df[col].astype(float)

        # 重命名和选择需要的列（包含number_of_trades以保持与预测脚本一致）
        df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume', 'number_of_trades']].copy()
        df.rename(columns={'timestamp': 'timestamp_utc'}, inplace=True)
        df.set_index('timestamp_utc', inplace=True)

        # 按时间排序并去重
        df = df.sort_index().drop_duplicates()

        print(f"成功获取 {len(df)} 条历史K线数据，时间范围: {df.index[0]} 到 {df.index[-1]}")
        return df

    except Exception as e:
        print(f"获取历史数据失败: {e}")
        return None

def prepare_actual_targets(historical_data, prediction_window=1):
    """
    从历史数据计算实际的目标值（真实的涨跌结果）
    
    Args:
        historical_data: 历史K线数据
        prediction_window: 预测窗口期
    
    Returns:
        pandas.Series: 实际目标值
    """
    try:
        df = historical_data.copy()
        
        # 计算未来回报率
        df['future_return'] = df['close'].pct_change(periods=-prediction_window).shift(-prediction_window)
        
        # 使用与训练时完全相同的逻辑计算动态阈值
        # 但对于实时数据，需要调整窗口大小以适应可用数据量
        available_data = len(df)
        if available_data >= 2000:
            window = 2000
            min_periods = 500
        elif available_data >= 500:
            window = available_data
            min_periods = min(500, available_data // 4)
        else:
            window = available_data
            min_periods = max(10, available_data // 10)

        threshold = df['future_return'].rolling(window=window, min_periods=min_periods).std().mean() * 0.5

        # 如果仍然是NaN，使用固定阈值
        if pd.isna(threshold):
            threshold = 0.005  # 0.5%的固定阈值
            print(f"使用固定阈值: ±{threshold:.6f} 来验证预测（数据不足）")
        else:
            print(f"使用动态阈值: ±{threshold:.6f} 来验证预测（窗口={window}, 最小期数={min_periods}）")
        
        # 定义实际目标
        df['actual_target'] = 0
        df.loc[df['future_return'] > threshold, 'actual_target'] = 1
        
        return df['actual_target']
        
    except Exception as e:
        print(f"计算实际目标值失败: {e}")
        return None

def load_prediction_log():
    """
    加载预测日志
    """
    try:
        if not os.path.exists(PREDICTION_LOG_PATH):
            print("没有找到预测日志文件")
            return None
        
        log_df = pd.read_csv(PREDICTION_LOG_PATH, index_col=0, parse_dates=True)
        log_df = log_df[~log_df.index.duplicated(keep='last')]  # 去重
        
        print(f"成功加载 {len(log_df)} 条预测记录")
        return log_df
        
    except Exception as e:
        print(f"加载预测日志失败: {e}")
        return None

def evaluate_predictions(prediction_log, actual_targets, historical_data):
    """
    评估预测结果的准确性（重新计算特征确保一致性）

    Args:
        prediction_log: 预测日志DataFrame
        actual_targets: 实际目标值Series
        historical_data: 历史K线数据DataFrame

    Returns:
        dict: 评估结果
    """
    try:
        # 【关键修复】从历史数据重新计算特征，确保与训练时一致
        print("正在从历史数据重新计算特征...")
        historical_featured = create_features(historical_data)

        if historical_featured.empty:
            print("历史数据特征工程失败")
            return None

        # 合并预测日志、实际结果和重新计算的特征
        # 首先合并预测和实际结果
        evaluation_df = prediction_log.join(actual_targets, how='inner')
        evaluation_df.dropna(subset=['actual_target'], inplace=True)

        if evaluation_df.empty:
            print("无法将预测日志与实际结果对齐")
            return None

        # 然后合并重新计算的特征
        # 只保留预测日志中的核心列，特征从历史数据中获取
        core_columns = ['close_price', 'prediction', 'prediction_proba_down', 'prediction_proba_up', 'prediction_confidence', 'actual_target']
        evaluation_core = evaluation_df[core_columns].copy()

        # 从历史特征数据中获取对应时间点的特征
        evaluation_with_features = evaluation_core.join(historical_featured, how='inner')
        evaluation_with_features.dropna(inplace=True)

        if evaluation_with_features.empty:
            print("无法将预测结果与历史特征对齐")
            return None

        evaluation_with_features['actual_target'] = evaluation_with_features['actual_target'].astype(int)
        evaluation_with_features['is_correct'] = (evaluation_with_features['prediction'] == evaluation_with_features['actual_target']).astype(int)

        # 计算评估指标
        total_predictions = len(evaluation_with_features)
        correct_predictions = evaluation_with_features['is_correct'].sum()
        accuracy = correct_predictions / total_predictions
        error_samples = evaluation_with_features[evaluation_with_features['is_correct'] == 0]

        result = {
            'total_predictions': total_predictions,
            'correct_predictions': correct_predictions,
            'accuracy': accuracy,
            'error_samples': error_samples,
            'evaluation_df': evaluation_with_features
        }

        print(f"验证了 {total_predictions} 条预测（包含完整特征）")
        print(f"正确预测: {correct_predictions} 条")
        print(f"准确率: {accuracy:.2%}")
        print(f"错误样本: {len(error_samples)} 条")

        return result

    except Exception as e:
        print(f"评估预测结果失败: {e}")
        return None

def retrain_model_with_reflection(evaluation_result, min_error_samples=10):
    """
    使用反思数据进行增量训练（优化版）

    Args:
        evaluation_result: 评估结果字典
        min_error_samples: 最少错误样本数量，低于此数量不进行重训练

    Returns:
        bool: 是否成功重训练
    """
    try:
        error_samples = evaluation_result['error_samples']
        evaluation_df = evaluation_result['evaluation_df']

        if len(error_samples) < min_error_samples:
            print(f"错误样本数量({len(error_samples)})少于最小要求({min_error_samples})，跳过重训练")
            return False

        print(f"\n--- 开始增量训练 ---")
        print(f"使用 {len(evaluation_df)} 条反思数据进行训练")

        # 加载模型和工具
        model = joblib.load(os.path.join(MODEL_DIR, "lgbm_model.joblib"))
        scaler = joblib.load(os.path.join(MODEL_DIR, "scaler.joblib"))
        feature_columns = joblib.load(os.path.join(MODEL_DIR, "feature_columns.joblib"))

        # 准备训练数据
        X_reflection = evaluation_df[feature_columns]
        y_reflection = evaluation_df['actual_target']

        # 定义样本权重：错误样本权重更高
        sample_weights = evaluation_df['is_correct'].apply(lambda x: 1 if x == 1 else 3).values

        X_reflection_scaled = scaler.transform(X_reflection)

        # 创建LightGBM数据集
        train_data = lgb.Dataset(X_reflection_scaled, label=y_reflection, weight=sample_weights)

        # 获取模型参数并清理不兼容的参数
        params = model.get_params()
        params['metric'] = 'binary_logloss'
        params.pop('n_estimators', None)
        params.pop('importance_type', None)

        # --- 核心优化：直接使用增量训练 ---
        # 1. 使用模型的booster_属性进行增量训练
        new_booster = lgb.train(
            params=params,
            train_set=train_data,
            init_model=model.booster_,  # 传入Booster对象而不是整个模型
            num_boost_round=200,
            valid_sets=[train_data],
            callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)]
        )

        # 2. 创建新的模型实例并设置booster（兼容性更好）
        # 某些LightGBM版本不允许直接设置booster_属性
        updated_model = lgb.LGBMClassifier(**model.get_params())

        # 使用私有属性设置（更兼容的方式）
        try:
            # 尝试直接设置
            updated_model._Booster = new_booster
            updated_model.booster_ = new_booster
        except AttributeError:
            # 如果直接设置失败，使用临时文件方法
            temp_booster_path = os.path.join(MODEL_DIR, "temp_booster.txt")
            new_booster.save_model(temp_booster_path)

            # 先用少量数据初始化模型结构
            sample_size = min(10, len(X_reflection_scaled))
            updated_model.fit(X_reflection_scaled[:sample_size], y_reflection[:sample_size])

            # 然后加载真正的booster
            updated_model._Booster = lgb.Booster(model_file=temp_booster_path)
            os.remove(temp_booster_path)

        # 使用更新后的模型
        model = updated_model

        # 3. 保存更新后的模型
        try:
            new_model_iteration = new_booster.current_iteration()
        except:
            new_model_iteration = 200  # 默认值

        new_model_path = os.path.join(MODEL_DIR, f"lgbm_model_iter_{new_model_iteration}.joblib")
        joblib.dump(model, new_model_path)
        joblib.dump(model, os.path.join(MODEL_DIR, "lgbm_model.joblib"))  # 覆盖最新模型

        print(f"模型已更新并保存，新版本迭代次数: {new_model_iteration}")
        return True

    except Exception as e:
        print(f"增量训练失败: {e}")
        return False

def cleanup_logs(evaluation_df):
    """
    清理日志文件，将已处理的数据存档
    """
    try:
        # 将评估数据存档
        if os.path.exists(REFLECTION_DATA_PATH):
            evaluation_df.to_csv(REFLECTION_DATA_PATH, mode='a', header=False)
        else:
            evaluation_df.to_csv(REFLECTION_DATA_PATH)
        
        # 清空预测日志
        if os.path.exists(PREDICTION_LOG_PATH):
            os.remove(PREDICTION_LOG_PATH)
        
        print("日志已清理，反思数据已存档")
        
    except Exception as e:
        print(f"清理日志失败: {e}")

def main():
    """
    主函数：执行反思学习流程
    """
    print(f"\n=== BTC价格预测反思学习 - {datetime.now()} ===")

    # 1. 检查预测日志是否存在且有足够数据
    prediction_log = load_prediction_log()
    if prediction_log is None:
        print("没有预测日志，无法进行反思学习")
        return

    if len(prediction_log) < MIN_PREDICTION_SAMPLES:
        print(f"预测样本数量({len(prediction_log)})不足，需要至少{MIN_PREDICTION_SAMPLES}条记录")
        print("跳过本次反思学习，等待更多预测数据")
        return

    # 2. 获取历史数据用于验证
    print(f"\n--- 获取历史数据验证预测结果 ---")
    historical_data = fetch_historical_klines()
    if historical_data is None:
        print("无法获取历史数据，跳过反思学习")
        return

    # 3. 计算实际目标值
    print(f"\n--- 计算实际涨跌结果 ---")
    actual_targets = prepare_actual_targets(historical_data, PREDICTION_WINDOW)
    if actual_targets is None:
        print("无法计算实际目标值，跳过反思学习")
        return

    # 4. 评估预测结果
    print(f"\n--- 评估预测准确性 ---")
    evaluation_result = evaluate_predictions(prediction_log, actual_targets, historical_data)
    if evaluation_result is None:
        print("预测评估失败，跳过反思学习")
        return

    # 5. 根据评估结果决定是否重训练
    accuracy = evaluation_result['accuracy']
    error_count = len(evaluation_result['error_samples'])

    print(f"\n--- 反思学习决策 ---")
    print(f"当前准确率: {accuracy:.2%}")
    print(f"错误样本数: {error_count}")
    print(f"准确率阈值: {ACCURACY_THRESHOLD:.1%}")
    print(f"最少错误样本要求: {MIN_ERROR_SAMPLES}")

    should_retrain = (accuracy < ACCURACY_THRESHOLD and error_count >= MIN_ERROR_SAMPLES)

    if should_retrain:
        print(f"\n触发重训练条件，开始增量学习...")
        retrain_success = retrain_model_with_reflection(evaluation_result, MIN_ERROR_SAMPLES)
        if retrain_success:
            print("✅ 模型重训练成功")
        else:
            print("❌ 模型重训练失败")
    else:
        if accuracy >= ACCURACY_THRESHOLD:
            print(f"✅ 准确率({accuracy:.2%})良好，暂不重训练")
        else:
            print(f"⚠️ 准确率较低但错误样本不足，暂不重训练")

    # 6. 清理日志
    print(f"\n--- 清理预测日志 ---")
    cleanup_logs(evaluation_result['evaluation_df'])

    print(f"\n🎉 反思学习完成 - {datetime.now()}")

if __name__ == "__main__":
    main()
